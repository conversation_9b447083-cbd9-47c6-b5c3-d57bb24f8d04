<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsInvoiceMatchMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
        <result column="case_times" jdbcType="INTEGER" property="caseTimes" />
        <result column="match_type" jdbcType="VARCHAR" property="matchType" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="id_ahcs_invoice_info" jdbcType="VARCHAR" property="idAhcsInvoiceInfo" />
        <result column="match_sign" jdbcType="TINYINT" property="matchSign" />
        <result column="fail_desc" jdbcType="VARCHAR" property="failDesc" />
        <result column="match_result" jdbcType="LONGVARCHAR" property="matchResult" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, report_no, case_times, match_type, config_id, id_ahcs_invoice_info, match_sign, fail_desc, match_result, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_INVOICE_MATCH
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_INVOICE_MATCH (id, report_no, case_times, match_type, config_id, id_ahcs_invoice_info, match_sign, fail_desc, match_result, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{caseTimes,jdbcType=INTEGER}, #{matchType,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{idAhcsInvoiceInfo,jdbcType=VARCHAR}, #{matchSign,jdbcType=TINYINT}, #{failDesc,jdbcType=VARCHAR}, #{matchResult,jdbcType=LONGVARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_INVOICE_MATCH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="reportNo != null">
                report_no,
            </if>
            <if test="caseTimes != null">
                case_times,
            </if>
            <if test="matchType != null">
                match_type,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="idAhcsInvoiceInfo != null">
                id_ahcs_invoice_info,
            </if>
            <if test="matchSign != null">
                match_sign,
            </if>
            <if test="failDesc != null">
                fail_desc,
            </if>
            <if test="matchResult != null">
                match_result,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                #{caseTimes,jdbcType=INTEGER},
            </if>
            <if test="matchType != null">
                #{matchType,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="idAhcsInvoiceInfo != null">
                #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            </if>
            <if test="matchSign != null">
                #{matchSign,jdbcType=TINYINT},
            </if>
            <if test="failDesc != null">
                #{failDesc,jdbcType=VARCHAR},
            </if>
            <if test="matchResult != null">
                #{matchResult,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch">
        update CLMS_INVOICE_MATCH
        set id = #{id,jdbcType=VARCHAR},
            report_no = #{reportNo,jdbcType=VARCHAR},
            case_times = #{caseTimes,jdbcType=INTEGER},
            match_type = #{matchType,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            match_sign = #{matchSign,jdbcType=TINYINT},
            fail_desc = #{failDesc,jdbcType=VARCHAR},
            match_result = #{matchResult,jdbcType=LONGVARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch">
        update CLMS_INVOICE_MATCH
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                report_no = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes,jdbcType=INTEGER},
            </if>
            <if test="matchType != null">
                match_type = #{matchType,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="idAhcsInvoiceInfo != null">
                id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            </if>
            <if test="matchSign != null">
                match_sign = #{matchSign,jdbcType=TINYINT},
            </if>
            <if test="failDesc != null">
                fail_desc = #{failDesc,jdbcType=VARCHAR},
            </if>
            <if test="matchResult != null">
                match_result = #{matchResult,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_INVOICE_MATCH
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_INVOICE_MATCH
    </select>

</mapper>
