package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 药品模板药品表
 */
@ApiModel("药品模板药品表")
@Getter
@Setter
public class ClmsMedicinePackageDetail {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 包外键id
     */
    @ApiModelProperty("包外键id")
    private Integer packageId;

    /**
     * 药品名称
     */
    @ApiModelProperty("药品名称")
    private String medicineName;

    /**
     * 药品分类
     */
    @ApiModelProperty("药品分类")
    private String medicineCategory;

    /**
     * 适应症
     */
    @ApiModelProperty("适应症")
    private String indication;

    /**
     * 有效标志
     */
    @ApiModelProperty("有效标志")
    private Boolean validFlag;

    /**
     * 备注描述
     */
    @ApiModelProperty("备注描述")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
