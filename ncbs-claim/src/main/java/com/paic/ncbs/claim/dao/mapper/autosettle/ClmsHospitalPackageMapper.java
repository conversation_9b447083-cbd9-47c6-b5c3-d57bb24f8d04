package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 医院模板包表Mapper
 */
public interface ClmsHospitalPackageMapper {

    /**
     * 根据主键查询
     */
    ClmsHospitalPackage selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsHospitalPackage record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsHospitalPackage record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsHospitalPackage record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsHospitalPackage record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsHospitalPackage> selectAll();

}
