package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsLimitShare;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-费用共享约束表Mapper
 */
public interface ClmsLimitShareMapper {

    /**
     * 根据主键查询
     */
    ClmsLimitShare selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsLimitShare record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsLimitShare record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsLimitShare record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsLimitShare record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsLimitShare> selectAll();

}
