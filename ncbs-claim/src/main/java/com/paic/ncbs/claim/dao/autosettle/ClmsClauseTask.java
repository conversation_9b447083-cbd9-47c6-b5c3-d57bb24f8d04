package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 流程任务表
 */
@ApiModel("流程任务表")
@Getter
@Setter
public class ClmsClauseTask {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 上一个任务id
     */
    @ApiModelProperty("上一个任务id")
    private Integer flowInId;

    /**
     * 理赔配置id
     */
    @ApiModelProperty("理赔配置id")
    private Integer configId;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer versionNo;

    /**
     * 任务节点
     */
    @ApiModelProperty("任务节点")
    private String taskNode;

    /**
     * 流入时间
     */
    @ApiModelProperty("流入时间")
    private Date flowInTime;

    /**
     * 开始处理时间
     */
    @ApiModelProperty("开始处理时间")
    private Date startTime;

    /**
     * 流出时间
     */
    @ApiModelProperty("流出时间")
    private Date flowOutTime;

    /**
     * 节点状态
     */
    @ApiModelProperty("节点状态")
    private String nodeStatus;

    /**
     * 任务处理人
     */
    @ApiModelProperty("任务处理人")
    private String taskUserCode;

    /**
     * 审核意见
     */
    @ApiModelProperty("审核意见")
    private String reviewOpinion;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
