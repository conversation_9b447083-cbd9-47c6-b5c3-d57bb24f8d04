package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 医疗ICD目录关系表
 */
@ApiModel("医疗ICD目录关系表")
@Getter
@Setter
public class ClmsMedicalIcdDir {

    /**
     * 目录id
     */
    @ApiModelProperty("目录id")
    private Integer id;

    /**
     * 国际ICD编码
     */
    @ApiModelProperty("国际ICD编码")
    private String dianoseCode;

    /**
     * 目录id
     */
    @ApiModelProperty("目录id")
    private Integer dirId;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
