package com.paic.ncbs.claim.model.dto.match;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 发票匹责DTO
 */
@Data
public class InvoiceMatchDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 主键 */
    private String idInvoiceMatch;
    
    /** 报案号 */
    private String reportNo;
    
    /** 赔付次数 */
    private Integer caseTimes;
    
    /** 发票ID */
    private String idAhcsBillInfo;
    
    /** 发票号 */
    private String billNo;
    
    /** 产品代码 */
    private String productCode;
    
    /** 方案代码 */
    private String planCode;
    
    /** 责任代码 */
    private String dutyCode;
    
    /** 责任明细代码 */
    private String dutyDetailCode;
    
    /** 匹责类型(MANUAL:人工匹责 AUTO:自动匹责) */
    private String matchType;
    
    /** 匹责状态(SUCCESS:成功 FAILED:失败) */
    private String matchStatus;
    
    /** 匹责原因 */
    private String matchReason;
    
    /** 创建人 */
    private String createdBy;
    
    /** 创建时间 */
    private Date createdDate;
    
    /** 更新人 */
    private String updatedBy;
    
    /** 更新时间 */
    private Date updatedDate;
    
    /** 归档时间 */
    private Date archiveTime;
}
