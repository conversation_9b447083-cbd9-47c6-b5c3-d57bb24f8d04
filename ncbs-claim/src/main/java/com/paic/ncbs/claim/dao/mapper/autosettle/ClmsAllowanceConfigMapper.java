package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceConfig;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-津贴类约束表Mapper
 */
public interface ClmsAllowanceConfigMapper {

    /**
     * 根据主键查询
     */
    ClmsAllowanceConfig selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsAllowanceConfig record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsAllowanceConfig record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsAllowanceConfig record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsAllowanceConfig record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsAllowanceConfig> selectAll();

}
