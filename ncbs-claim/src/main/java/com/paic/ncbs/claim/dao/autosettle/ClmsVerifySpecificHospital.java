package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款核责配置-特定医院表
 */
@ApiModel("条款核责配置-特定医院表")
@Getter
@Setter
public class ClmsVerifySpecificHospital {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 医院核责规则id
     */
    @ApiModelProperty("医院核责规则id")
    private Integer vHospitalId;

    /**
     * 特定类型
     */
    @ApiModelProperty("特定类型")
    private String specificType;

    /**
     * 医院代码
     */
    @ApiModelProperty("医院代码")
    private String hospitalCode;

    /**
     * 医院名称
     */
    @ApiModelProperty("医院名称")
    private String hospitalName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
