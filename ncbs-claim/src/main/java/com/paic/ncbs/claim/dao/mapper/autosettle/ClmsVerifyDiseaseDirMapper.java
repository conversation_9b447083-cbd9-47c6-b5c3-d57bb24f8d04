package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyDiseaseDir;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-疾病范围目录表Mapper
 */
public interface ClmsVerifyDiseaseDirMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyDiseaseDir selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyDiseaseDir record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyDiseaseDir record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyDiseaseDir record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyDiseaseDir record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyDiseaseDir> selectAll();

}
