package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseTask;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 流程任务表Mapper
 */
public interface ClmsClauseTaskMapper {

    /**
     * 根据主键查询
     */
    ClmsClauseTask selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsClauseTask record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsClauseTask record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsClauseTask record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsClauseTask record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsClauseTask> selectAll();

}
