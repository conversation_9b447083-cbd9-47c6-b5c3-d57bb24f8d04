package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDisease;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款匹责疾病范围表Mapper
 */
public interface ClmsClauseMatchDiseaseMapper {

    /**
     * 根据主键查询
     */
    ClmsClauseMatchDisease selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsClauseMatchDisease record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsClauseMatchDisease record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsClauseMatchDisease record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsClauseMatchDisease record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsClauseMatchDisease> selectAll();

}
