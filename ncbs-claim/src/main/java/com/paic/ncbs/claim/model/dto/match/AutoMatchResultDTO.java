package com.paic.ncbs.claim.model.dto.match;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 自动匹责结果DTO
 */
@Data
public class AutoMatchResultDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 报案号 */
    private String reportNo;
    
    /** 赔付次数 */
    private Integer caseTimes;
    
    /** 匹责是否成功 */
    private boolean success;
    
    /** 匹责失败原因 */
    private String failReason;
    
    /** 匹责结果列表 */
    private List<InvoiceMatchDTO> matchResults;
}
