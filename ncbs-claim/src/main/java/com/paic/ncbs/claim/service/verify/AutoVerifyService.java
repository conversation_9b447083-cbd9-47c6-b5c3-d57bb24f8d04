package com.paic.ncbs.claim.service.verify;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyRequestDTO;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyResultDTO;

/**
 * 自动核责服务接口
 */
public interface AutoVerifyService {
    
    /**
     * 自动核责
     * @param request 自动核责请求
     * @return 自动核责结果
     * @throws GlobalBusinessException
     */
    AutoVerifyResultDTO autoVerify(AutoVerifyRequestDTO request) throws GlobalBusinessException;
    
    /**
     * 检查是否需要自动核责
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param productCode 产品代码
     * @return 是否需要自动核责
     */
    boolean needAutoVerify(String reportNo, Integer caseTimes, String productCode);
    
    /**
     * 查询核责结果
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 核责结果
     */
    AutoVerifyResultDTO getVerifyResults(String reportNo, Integer caseTimes);
}
