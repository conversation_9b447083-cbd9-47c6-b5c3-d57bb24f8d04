package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsCaculateLog;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 自动理算日志主表Mapper
 */
public interface ClmsCaculateLogMapper {

    /**
     * 根据主键查询
     */
    ClmsCaculateLog selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsCaculateLog record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsCaculateLog record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsCaculateLog record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsCaculateLog record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsCaculateLog> selectAll();

}
