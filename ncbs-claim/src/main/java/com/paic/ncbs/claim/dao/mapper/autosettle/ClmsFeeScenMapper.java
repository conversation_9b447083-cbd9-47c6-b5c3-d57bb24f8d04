package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsFeeScen;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-理算费用类场景配置表Mapper
 */
public interface ClmsFeeScenMapper {

    /**
     * 根据主键查询
     */
    ClmsFeeScen selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsFeeScen record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsFeeScen record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsFeeScen record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsFeeScen record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsFeeScen> selectAll();

}
