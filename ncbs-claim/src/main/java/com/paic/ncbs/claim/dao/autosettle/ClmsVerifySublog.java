package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 自动核责日志子表
 */
@ApiModel("自动核责日志子表")
@Getter
@Setter
public class ClmsVerifySublog {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 日志id
     */
    @ApiModelProperty("日志id")
    private Integer verifyLogId;

    /**
     * 核责等级
     */
    @ApiModelProperty("核责等级")
    private String verifyLevel;

    /**
     * 核责分类
     */
    @ApiModelProperty("核责分类")
    private String verifyClass;

    /**
     * 发票信息表主键
     */
    @ApiModelProperty("发票信息表主键")
    private String idAhcsInvoiceInfo;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 发票项目类别（当前仅针对药品分类）
     */
    @ApiModelProperty("发票项目类别（当前仅针对药品分类）")
    private String invoiceItemType;

    /**
     * 发票项目代码（当前仅针对药品分类）
     */
    @ApiModelProperty("发票项目代码（当前仅针对药品分类）")
    private String invoiceItemCode;

    /**
     * 发票明细名称
     */
    @ApiModelProperty("发票明细名称")
    private String invoiceDetailName;

    /**
     * 是否免责
     */
    @ApiModelProperty("是否免责")
    private Boolean disclaim;

    /**
     * 免责描述
     */
    @ApiModelProperty("免责描述")
    private String disclaimDesc;

    /**
     * 免责金额
     */
    @ApiModelProperty("免责金额")
    private BigDecimal disclaimValue;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
