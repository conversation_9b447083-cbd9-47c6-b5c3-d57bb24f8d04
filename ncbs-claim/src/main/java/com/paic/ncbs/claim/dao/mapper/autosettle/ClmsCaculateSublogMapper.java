package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsCaculateSublog;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 自动理算日志子表Mapper
 */
public interface ClmsCaculateSublogMapper {

    /**
     * 根据主键查询
     */
    ClmsCaculateSublog selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsCaculateSublog record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsCaculateSublog record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsCaculateSublog record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsCaculateSublog record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsCaculateSublog> selectAll();

}
