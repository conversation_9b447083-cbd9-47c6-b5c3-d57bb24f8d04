package com.paic.ncbs.claim.service.verify.impl;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.autosettle.*;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyRequestDTO;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyResultDTO;
import com.paic.ncbs.claim.service.verify.AutoVerifyService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动核责服务实现类
 */
@Service
public class AutoVerifyServiceImpl implements AutoVerifyService {
    
    @Autowired
    private ClmsVerifyLogMapper verifyLogMapper;
    
    @Autowired
    private ClmsVerifySublogMapper verifySublogMapper;
    
    @Autowired
    private ClmsClauseConfigMapper clauseConfigMapper;
    
    @Autowired
    private ClmsVerifyHospitalMapper verifyHospitalMapper;
    
    @Autowired
    private ClmsVerifyDiseaseMapper verifyDiseaseMapper;
    
    @Autowired
    private ClmsVerifyCatalog3Mapper verifyCatalog3Mapper;
    
    @Autowired
    private ClmsVerifyOtherMapper verifyOtherMapper;
    
    @Override
    @Transactional
    public AutoVerifyResultDTO autoVerify(AutoVerifyRequestDTO request) throws GlobalBusinessException {
        LogUtil.audit("开始自动核责，报案号：{}，赔付次数：{}", request.getReportNo(), request.getCaseTimes());
        
        AutoVerifyResultDTO result = new AutoVerifyResultDTO();
        result.setReportNo(request.getReportNo());
        result.setCaseTimes(request.getCaseTimes());
        result.setVerifyResults(new ArrayList<>());
        
        try {
            // 1. 检查是否需要自动核责
            if (!needAutoVerify(request.getReportNo(), request.getCaseTimes(), request.getProductCode())) {
                result.setSuccess(false);
                result.setFailReason("不需要自动核责");
                return result;
            }
            
            // 2. 检查匹责结果
            if (CollectionUtils.isEmpty(request.getMatchResults())) {
                result.setSuccess(false);
                result.setFailReason("无匹责结果，无法进行核责");
                LogUtil.audit("无匹责结果，无法进行核责");
                return result;
            }
            
            // 3. 查找有效的理赔配置
            Integer configId = clauseConfigMapper.getEffectiveConfigId(request.getProductCode(), request.getPlanCode());
            if (configId == null) {
                result.setSuccess(false);
                result.setFailReason("未找到有效的理赔配置项");
                LogUtil.audit("未找到有效的理赔配置项，产品代码：{}，方案代码：{}", request.getProductCode(), request.getPlanCode());
                return result;
            }
            
            // 4. 执行自动核责逻辑
            List<Object> verifyResults = performAutoVerify(request, configId);
            
            // 5. 保存核责结果
            saveVerifyResults(request, verifyResults, configId);
            
            result.setSuccess(true);
            LogUtil.audit("自动核责成功，报案号：{}，赔付次数：{}", request.getReportNo(), request.getCaseTimes());
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setFailReason("自动核责异常：" + e.getMessage());
            LogUtil.audit("自动核责异常：{}", e.getMessage());
            throw new GlobalBusinessException("自动核责失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行自动核责逻辑
     */
    private List<Object> performAutoVerify(AutoVerifyRequestDTO request, Integer configId) {
        List<Object> results = new ArrayList<>();
        
        // 遍历匹责结果明细列表（逐张票据vs责任明细）
        for (Object matchResult : request.getMatchResults()) {
            // 1. 医院范围核定（发票层级）
            Object hospitalVerifyResult = verifyHospital(matchResult, configId);
            if (hospitalVerifyResult != null) {
                results.add(hospitalVerifyResult);
            }
            
            // 2. 疾病范围核定（发票层级）
            Object diseaseVerifyResult = verifyDisease(matchResult, configId);
            if (diseaseVerifyResult != null) {
                results.add(diseaseVerifyResult);
            }
            
            // 3. 三目录除外核定（发票明细层级）
            Object catalog3VerifyResult = verifyCatalog3(matchResult, configId);
            if (catalog3VerifyResult != null) {
                results.add(catalog3VerifyResult);
            }
            
            // 4. 其他核责项核定（发票层级&案件层级）
            Object otherVerifyResult = verifyOther(matchResult, configId);
            if (otherVerifyResult != null) {
                results.add(otherVerifyResult);
            }
        }
        
        return results;
    }
    
    /**
     * 医院范围核定
     */
    private Object verifyHospital(Object matchResult, Integer configId) {
        LogUtil.audit("开始医院范围核定");
        
        // 1. 找到config_id关联的核责配置的医院范围部分核定标准
        Object hospitalConfig = findHospitalConfig(matchResult, configId);
        
        if (hospitalConfig == null) {
            // 无医院核定标准时，当前票据的医院范围核定为非免责
            LogUtil.audit("无医院核定标准，核定为非免责");
            return null;
        }
        
        // 2. 使用发票医院关联找到医院详细信息
        Object hospitalInfo = getHospitalInfo(matchResult);
        
        // 3. 使用医院范围核定标准核定票据医院信息
        return performHospitalVerify(hospitalInfo, hospitalConfig);
    }
    
    /**
     * 疾病范围核定
     */
    private Object verifyDisease(Object matchResult, Integer configId) {
        LogUtil.audit("开始疾病范围核定");
        
        // 1. 找到config_id关联的核责配置的疾病范围部分核定标准
        Object diseaseConfig = findDiseaseConfig(matchResult, configId);
        
        if (diseaseConfig == null) {
            // 无疾病核定标准时，当前票据的疾病范围核定为非免责
            LogUtil.audit("无疾病核定标准，核定为非免责");
            return null;
        }
        
        // 2. 使用发票信息关联找到关联主诊断ICD代码
        String mainDiagnoseIcd = getMainDiagnoseIcd(matchResult);
        
        // 3. 使用疾病范围核定标准核定票据疾病范围
        return performDiseaseVerify(mainDiagnoseIcd, diseaseConfig);
    }
    
    /**
     * 三目录除外核定
     */
    private Object verifyCatalog3(Object matchResult, Integer configId) {
        LogUtil.audit("开始三目录除外核定");
        
        // 1. 找到config_id关联的核责配置的三目录除外部分核定标准
        Object catalog3Config = findCatalog3Config(matchResult, configId);
        
        if (catalog3Config == null) {
            // 无三目录除外部分标准时，当前票据的核定为非免责
            LogUtil.audit("无三目录除外标准，核定为非免责");
            return null;
        }
        
        // 2. 使用票据关联找到票据明细清单（医疗费用明细）
        List<Object> billDetailList = getBillDetailList(matchResult);
        
        if (CollectionUtils.isEmpty(billDetailList)) {
            // 票据明细清单为空，当前发票核定为非免责
            LogUtil.audit("票据明细清单为空，核定为非免责");
            return null;
        }
        
        // 3. 循环票据明细逐行做核定
        return performCatalog3Verify(billDetailList, catalog3Config);
    }
    
    /**
     * 其他核责项核定
     */
    private Object verifyOther(Object matchResult, Integer configId) {
        LogUtil.audit("开始其他核责项核定");
        
        // 1. 找到config_id关联的核责配置的其他核责部分核定标准
        Object otherConfig = findOtherConfig(matchResult, configId);
        
        if (otherConfig == null) {
            // 无其他核责部分标准时，跳出其他核责逻辑
            LogUtil.audit("无其他核责标准，跳过其他核责");
            return null;
        }
        
        // 2. 循环其他核责项目
        return performOtherVerify(matchResult, otherConfig);
    }
    
    /**
     * 保存核责结果
     */
    @Transactional
    private void saveVerifyResults(AutoVerifyRequestDTO request, List<Object> verifyResults, Integer configId) {
        // 保存核责日志主表
        Object verifyLog = createVerifyLog(request, configId);
        verifyLogMapper.insert(verifyLog);
        
        // 保存核责日志子表
        for (Object result : verifyResults) {
            Object verifySublog = createVerifySublog(result, verifyLog);
            verifySublogMapper.insert(verifySublog);
        }
        
        LogUtil.audit("保存核责结果成功，报案号：{}，赔付次数：{}", request.getReportNo(), request.getCaseTimes());
    }
    
    @Override
    public boolean needAutoVerify(String reportNo, Integer caseTimes, String productCode) {
        // 1. 检查产品线是否为意外险或健康险
        if (!isHealthOrAccidentProduct(productCode)) {
            LogUtil.audit("产品线非意外险/健康险，不需要自动核责，产品代码：{}", productCode);
            return false;
        }
        
        return true;
    }
    
    @Override
    public AutoVerifyResultDTO getVerifyResults(String reportNo, Integer caseTimes) {
        // 查询核责结果
        Object verifyLog = verifyLogMapper.selectByReportNoAndCaseTimes(reportNo, caseTimes);
        if (verifyLog == null) {
            return null;
        }
        
        List<Object> verifySublogList = verifySublogMapper.selectByVerifyLogId(getVerifyLogId(verifyLog));
        
        AutoVerifyResultDTO result = new AutoVerifyResultDTO();
        result.setReportNo(reportNo);
        result.setCaseTimes(caseTimes);
        result.setSuccess(true);
        // 设置其他结果字段...
        
        return result;
    }
    
    // 以下是辅助方法，需要根据具体业务逻辑实现
    
    private boolean isHealthOrAccidentProduct(String productCode) {
        return StringUtils.isNotBlank(productCode) && 
               (productCode.startsWith("H") || productCode.startsWith("A"));
    }
    
    private Object findHospitalConfig(Object matchResult, Integer configId) {
        // 实现查找医院配置逻辑
        return null;
    }
    
    private Object getHospitalInfo(Object matchResult) {
        // 实现获取医院信息逻辑
        return null;
    }
    
    private Object performHospitalVerify(Object hospitalInfo, Object hospitalConfig) {
        // 实现医院核责逻辑
        return null;
    }
    
    private Object findDiseaseConfig(Object matchResult, Integer configId) {
        // 实现查找疾病配置逻辑
        return null;
    }
    
    private String getMainDiagnoseIcd(Object matchResult) {
        // 实现获取主诊断ICD逻辑
        return null;
    }
    
    private Object performDiseaseVerify(String mainDiagnoseIcd, Object diseaseConfig) {
        // 实现疾病核责逻辑
        return null;
    }
    
    private Object findCatalog3Config(Object matchResult, Integer configId) {
        // 实现查找三目录配置逻辑
        return null;
    }
    
    private List<Object> getBillDetailList(Object matchResult) {
        // 实现获取票据明细逻辑
        return new ArrayList<>();
    }
    
    private Object performCatalog3Verify(List<Object> billDetailList, Object catalog3Config) {
        // 实现三目录核责逻辑
        return null;
    }
    
    private Object findOtherConfig(Object matchResult, Integer configId) {
        // 实现查找其他配置逻辑
        return null;
    }
    
    private Object performOtherVerify(Object matchResult, Object otherConfig) {
        // 实现其他核责逻辑
        return null;
    }
    
    private Object createVerifyLog(AutoVerifyRequestDTO request, Integer configId) {
        // 实现创建核责日志逻辑
        return null;
    }
    
    private Object createVerifySublog(Object result, Object verifyLog) {
        // 实现创建核责子日志逻辑
        return null;
    }
    
    private Integer getVerifyLogId(Object verifyLog) {
        // 实现获取核责日志ID逻辑
        return null;
    }
}
