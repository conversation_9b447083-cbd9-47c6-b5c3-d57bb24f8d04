package com.paic.ncbs.claim.model.dto.verify;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 自动核责请求DTO
 */
@Data
public class AutoVerifyRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 报案号 */
    private String reportNo;
    
    /** 赔付次数 */
    private Integer caseTimes;
    
    /** 产品代码 */
    private String productCode;
    
    /** 方案代码 */
    private String planCode;
    
    /** 匹责结果列表 */
    private List<MatchResultForVerifyDTO> matchResults;
}

/**
 * 用于核责的匹责结果DTO
 */
@Data
class MatchResultForVerifyDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 发票ID */
    private String idAhcsBillInfo;
    
    /** 发票号 */
    private String billNo;
    
    /** 责任代码 */
    private String dutyCode;
    
    /** 责任明细代码 */
    private String dutyDetailCode;
    
    /** 发票信息 */
    private BillInfoForVerifyDTO billInfo;
}

/**
 * 用于核责的发票信息DTO
 */
@Data
class BillInfoForVerifyDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 发票类型 */
    private String billType;
    
    /** 医院代码 */
    private String hospitalCode;
    
    /** 医院名称 */
    private String hospitalName;
    
    /** 医院等级 */
    private String hospitalGrade;
    
    /** 医院级别 */
    private String hospitalLevel;
    
    /** 医院性质 */
    private String hospitalProperty;
    
    /** 就诊类型 */
    private String visitType;
    
    /** 主诊断ICD代码 */
    private String mainDiagnoseIcd;
    
    /** 主诊断名称 */
    private String mainDiagnoseName;
    
    /** 费用明细列表 */
    private List<BillDetailForVerifyDTO> billDetailList;
}

/**
 * 用于核责的费用明细DTO
 */
@Data
class BillDetailForVerifyDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 费用项目 */
    private String costCode;
    
    /** 费用名称 */
    private String costName;
    
    /** 费用类型 */
    private String costType;
    
    /** 费用金额 */
    private java.math.BigDecimal costAmount;
}
