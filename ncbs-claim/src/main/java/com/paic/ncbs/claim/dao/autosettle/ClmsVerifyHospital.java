package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款核责配置-医院范围表
 */
@ApiModel("条款核责配置-医院范围表")
@Getter
@Setter
public class ClmsVerifyHospital {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 核责等级
     */
    @ApiModelProperty("核责等级")
    private Byte verifyLevel;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 医院等级
     */
    @ApiModelProperty("医院等级")
    private String hospitalLevel;

    /**
     * 医院级别
     */
    @ApiModelProperty("医院级别")
    private String hospitalGrade;

    /**
     * 医院性质
     */
    @ApiModelProperty("医院性质")
    private String hospitalNature;

    /**
     * 就诊类型
     */
    @ApiModelProperty("就诊类型")
    private String medicalConsultation;

    /**
     * 医院区域类型
     */
    @ApiModelProperty("医院区域类型")
    private String hospitalRegion;

    /**
     * 除外省市
     */
    @ApiModelProperty("除外省市")
    private String exceptRegion;

    /**
     * 协议扩展-医院包ID
     */
    @ApiModelProperty("协议扩展-医院包ID")
    private Integer includePackageId;

    /**
     * 除外-医院包ID
     */
    @ApiModelProperty("除外-医院包ID")
    private Integer excludePackageId;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
