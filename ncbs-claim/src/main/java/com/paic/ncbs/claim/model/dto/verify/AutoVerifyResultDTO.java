package com.paic.ncbs.claim.model.dto.verify;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 自动核责结果DTO
 */
@Data
public class AutoVerifyResultDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 报案号 */
    private String reportNo;
    
    /** 赔付次数 */
    private Integer caseTimes;
    
    /** 核责是否成功 */
    private boolean success;
    
    /** 核责失败原因 */
    private String failReason;
    
    /** 核责结果列表 */
    private List<VerifyResultDetailDTO> verifyResults;
}

/**
 * 核责结果明细DTO
 */
@Data
class VerifyResultDetailDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 发票ID */
    private String idAhcsBillInfo;
    
    /** 发票号 */
    private String billNo;
    
    /** 责任代码 */
    private String dutyCode;
    
    /** 责任明细代码 */
    private String dutyDetailCode;
    
    /** 核责等级 */
    private String verifyLevel;
    
    /** 核责分类 */
    private String verifyClass;
    
    /** 是否免责 */
    private boolean isExempt;
    
    /** 免责原因 */
    private String exemptReason;
    
    /** 免责金额 */
    private BigDecimal exemptAmount;
    
    /** 明细核责结果列表 */
    private List<DetailVerifyResultDTO> detailResults;
}

/**
 * 明细核责结果DTO
 */
@Data
class DetailVerifyResultDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 费用项目 */
    private String costCode;
    
    /** 费用名称 */
    private String costName;
    
    /** 是否免责 */
    private boolean isExempt;
    
    /** 免责原因 */
    private String exemptReason;
    
    /** 免责金额 */
    private BigDecimal exemptAmount;
}
