package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmountFactor;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-差异化额度因子表Mapper
 */
public interface ClmsDifferAmountFactorMapper {

    /**
     * 根据主键查询
     */
    ClmsDifferAmountFactor selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsDifferAmountFactor record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsDifferAmountFactor record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsDifferAmountFactor record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsDifferAmountFactor record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsDifferAmountFactor> selectAll();

}
