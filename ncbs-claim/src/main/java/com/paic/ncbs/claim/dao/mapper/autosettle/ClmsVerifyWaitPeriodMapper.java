package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyWaitPeriod;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-等待期表Mapper
 */
public interface ClmsVerifyWaitPeriodMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyWaitPeriod selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyWaitPeriod record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyWaitPeriod record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyWaitPeriod record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyWaitPeriod record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyWaitPeriod> selectAll();

}
