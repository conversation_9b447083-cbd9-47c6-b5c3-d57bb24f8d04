package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 自动理算日志子表
 */
@ApiModel("自动理算日志子表")
@Getter
@Setter
public class ClmsCaculateSublog {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 日志id
     */
    @ApiModelProperty("日志id")
    private Integer caculateLogId;

    /**
     * 发票文件id
     */
    @ApiModelProperty("发票文件id")
    private String fileId;

    /**
     * 发票信息表主键
     */
    @ApiModelProperty("发票信息表主键")
    private String idAhcsInvoiceInfo;

    /**
     * 场景类型
     */
    @ApiModelProperty("场景类型")
    private String scenType;

    /**
     * 使用自动理算场景配置id
     */
    @ApiModelProperty("使用自动理算场景配置id")
    private Integer scenId;

    /**
     * 赔付比例
     */
    @ApiModelProperty("赔付比例")
    private BigDecimal payProportion;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 发票项目类别
     */
    @ApiModelProperty("发票项目类别")
    private String invoiceItemType;

    /**
     * 发票项目代码（当前仅针对药品分类）
     */
    @ApiModelProperty("发票项目代码（当前仅针对药品分类）")
    private String invoiceItemCode;

    /**
     * 发票项目名称（针对药品分类）
     */
    @ApiModelProperty("发票项目名称（针对药品分类）")
    private String invoiceItemName;

    /**
     * 发票明细代码
     */
    @ApiModelProperty("发票明细代码")
    private String invoiceDetailCode;

    /**
     * 发票明细名称
     */
    @ApiModelProperty("发票明细名称")
    private String invoiceDetailName;

    /**
     * 自费金额
     */
    @ApiModelProperty("自费金额")
    private BigDecimal selfPayAmount;

    /**
     * 部分自费金额
     */
    @ApiModelProperty("部分自费金额")
    private BigDecimal partialSelfPayAmount;

    /**
     * 理算不合理费用
     */
    @ApiModelProperty("理算不合理费用")
    private BigDecimal unreasonableAmount;

    /**
     * 第三方支付
     */
    @ApiModelProperty("第三方支付")
    private BigDecimal thirdPartyPayment;

    /**
     * 理算合理医疗费用
     */
    @ApiModelProperty("理算合理医疗费用")
    private BigDecimal reasonableMedicalExpense;

    /**
     * 理算赔付金额
     */
    @ApiModelProperty("理算赔付金额")
    private BigDecimal calculatedCompensationAmount;

    /**
     * 扣费金额
     */
    @ApiModelProperty("扣费金额")
    private BigDecimal deductionAmount;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
