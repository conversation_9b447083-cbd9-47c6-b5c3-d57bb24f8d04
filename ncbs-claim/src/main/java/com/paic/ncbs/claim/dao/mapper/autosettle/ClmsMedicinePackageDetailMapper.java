package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetail;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 药品模板药品表Mapper
 */
public interface ClmsMedicinePackageDetailMapper {

    /**
     * 根据主键查询
     */
    ClmsMedicinePackageDetail selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsMedicinePackageDetail record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsMedicinePackageDetail record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsMedicinePackageDetail record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsMedicinePackageDetail record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsMedicinePackageDetail> selectAll();

}
