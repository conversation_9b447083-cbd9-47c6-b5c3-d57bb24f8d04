package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款理算配置-差异化额度因子表
 */
@ApiModel("条款理算配置-差异化额度因子表")
@Getter
@Setter
public class ClmsDifferAmountFactor {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 差异化额度表id
     */
    @ApiModelProperty("差异化额度表id")
    private Integer daId;

    /**
     * 因子代码
     */
    @ApiModelProperty("因子代码")
    private String factorCode;

    /**
     * 因子名称
     */
    @ApiModelProperty("因子名称")
    private String factorName;

    /**
     * 匹配公式
     */
    @ApiModelProperty("匹配公式")
    private String famular;

    /**
     * 因子值
     */
    @ApiModelProperty("因子值")
    private String factorValues;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
