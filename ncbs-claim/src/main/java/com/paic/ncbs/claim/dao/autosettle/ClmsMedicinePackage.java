package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 药品模板包表
 */
@ApiModel("药品模板包表")
@Getter
@Setter
public class ClmsMedicinePackage {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 模板包名称
     */
    @ApiModelProperty("模板包名称")
    private String packageName;

    /**
     * 药品模板分类
     */
    @ApiModelProperty("药品模板分类")
    private String packageType;

    /**
     * 有效标志
     */
    @ApiModelProperty("有效标志")
    private Boolean validFlag;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
