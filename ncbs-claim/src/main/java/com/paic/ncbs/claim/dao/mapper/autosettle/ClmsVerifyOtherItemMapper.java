package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOtherItem;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-其他核责项字典表Mapper
 */
public interface ClmsVerifyOtherItemMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyOtherItem selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyOtherItem record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyOtherItem record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyOtherItem record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyOtherItem record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyOtherItem> selectAll();

}
