package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * 条款核责配置-三目录除外除外药品表
 */
@ApiModel("条款核责配置-三目录除外除外药品表")
@Getter
@Setter
public class ClmsVerifyCatalog3Medicine {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 核责三目录id
     */
    @ApiModelProperty("核责三目录id")
    private Integer catalog3Id;

    /**
     * 药品包ID
     */
    @ApiModelProperty("药品包ID")
    private Integer packageId;

    /**
     * 除外药所在包的id
     */
    @ApiModelProperty("除外药所在包的id")
    private Integer medicineId;

    /**
     * 药品名称
     */
    @ApiModelProperty("药品名称")
    private String medicineName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
