package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款匹责疾病范围表
 */
@ApiModel("条款匹责疾病范围表")
@Getter
@Setter
public class ClmsClauseMatchDisease {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 自动匹责id
     */
    @ApiModelProperty("自动匹责id")
    private Integer matchId;

    /**
     * 一级医疗目录id
     */
    @ApiModelProperty("一级医疗目录id")
    private Integer medicalDir1Id;

    /**
     * 一级医疗目录名称
     */
    @ApiModelProperty("一级医疗目录名称")
    private String medicalDir1Name;

    /**
     * 二级医疗目录id
     */
    @ApiModelProperty("二级医疗目录id")
    private Integer medicalDir2Id;

    /**
     * 二级医疗目录名称
     */
    @ApiModelProperty("二级医疗目录名称")
    private String medicalDir2Name;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
