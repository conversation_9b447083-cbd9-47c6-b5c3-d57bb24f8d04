package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOther;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-其他核责配置表Mapper
 */
public interface ClmsVerifyOtherMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyOther selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyOther record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyOther record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyOther record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyOther record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyOther> selectAll();

}
