package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyDisease;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-疾病范围表Mapper
 */
public interface ClmsVerifyDiseaseMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyDisease selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyDisease record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyDisease record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyDisease record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyDisease record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyDisease> selectAll();

}
