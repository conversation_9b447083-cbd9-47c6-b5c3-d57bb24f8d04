package com.paic.ncbs.claim.service.match;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.match.AutoMatchRequestDTO;
import com.paic.ncbs.claim.model.dto.match.AutoMatchResultDTO;
import com.paic.ncbs.claim.model.dto.match.InvoiceMatchDTO;

import java.util.List;

/**
 * 发票匹责服务接口
 */
public interface InvoiceMatchService {
    
    /**
     * 人工匹责保存
     * @param matchList 匹责结果列表
     * @param userUM 操作用户
     * @throws GlobalBusinessException
     */
    void saveManualMatch(List<InvoiceMatchDTO> matchList, String userUM) throws GlobalBusinessException;
    
    /**
     * 自动匹责
     * @param request 自动匹责请求
     * @return 自动匹责结果
     * @throws GlobalBusinessException
     */
    AutoMatchResultDTO autoMatch(AutoMatchRequestDTO request) throws GlobalBusinessException;
    
    /**
     * 查询匹责结果
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 匹责结果列表
     */
    List<InvoiceMatchDTO> getMatchResults(String reportNo, Integer caseTimes);
    
    /**
     * 查询发票的匹责结果
     * @param idAhcsBillInfo 发票ID
     * @return 匹责结果列表
     */
    List<InvoiceMatchDTO> getMatchResultsByBillId(String idAhcsBillInfo);
    
    /**
     * 删除匹责结果
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param userUM 操作用户
     */
    void deleteMatchResults(String reportNo, Integer caseTimes, String userUM);
    
    /**
     * 检查是否需要自动匹责
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param productCode 产品代码
     * @return 是否需要自动匹责
     */
    boolean needAutoMatch(String reportNo, Integer caseTimes, String productCode);
    
    /**
     * 验证人工匹责结果
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param productCode 产品代码
     * @throws GlobalBusinessException
     */
    void validateManualMatch(String reportNo, Integer caseTimes, String productCode) throws GlobalBusinessException;
}
