package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 药品模板包表Mapper
 */
public interface ClmsMedicinePackageMapper {

    /**
     * 根据主键查询
     */
    ClmsMedicinePackage selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsMedicinePackage record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsMedicinePackage record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsMedicinePackage record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsMedicinePackage record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsMedicinePackage> selectAll();

}
