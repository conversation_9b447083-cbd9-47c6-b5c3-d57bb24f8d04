package com.paic.ncbs.claim.controller.verify;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyRequestDTO;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyResultDTO;
import com.paic.ncbs.claim.service.verify.AutoVerifyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 自动核责控制器
 */
@RestController
@RequestMapping("/api/verify")
public class AutoVerifyController {
    
    @Autowired
    private AutoVerifyService autoVerifyService;
    
    /**
     * 自动核责
     */
    @PostMapping("/auto")
    public AutoVerifyResultDTO autoVerify(@RequestBody AutoVerifyRequestDTO request) {
        try {
            return autoVerifyService.autoVerify(request);
        } catch (Exception e) {
            LogUtil.audit("自动核责失败：{}", e.getMessage());
            throw new GlobalBusinessException("自动核责失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查是否需要自动核责
     */
    @GetMapping("/check/need-auto")
    public boolean needAutoVerify(@RequestParam String reportNo,
                                  @RequestParam Integer caseTimes,
                                  @RequestParam String productCode) {
        try {
            if (StringUtils.isBlank(reportNo) || caseTimes == null || StringUtils.isBlank(productCode)) {
                throw new GlobalBusinessException("报案号、赔付次数和产品代码不能为空");
            }
            
            return autoVerifyService.needAutoVerify(reportNo, caseTimes, productCode);
        } catch (Exception e) {
            LogUtil.audit("检查是否需要自动核责失败：{}", e.getMessage());
            throw new GlobalBusinessException("检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询核责结果
     */
    @GetMapping("/results")
    public AutoVerifyResultDTO getVerifyResults(@RequestParam String reportNo,
                                                @RequestParam Integer caseTimes) {
        try {
            if (StringUtils.isBlank(reportNo) || caseTimes == null) {
                throw new GlobalBusinessException("报案号和赔付次数不能为空");
            }
            
            return autoVerifyService.getVerifyResults(reportNo, caseTimes);
        } catch (Exception e) {
            LogUtil.audit("查询核责结果失败：{}", e.getMessage());
            throw new GlobalBusinessException("查询失败：" + e.getMessage());
        }
    }
}
