package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款理算配置-理算顺序
 */
@ApiModel("条款理算配置-理算顺序")
@Getter
@Setter
public class ClmsCalcSeq {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 理算顺序
     */
    @ApiModelProperty("理算顺序")
    private Integer calcSeq;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 关联责任代码
     */
    @ApiModelProperty("关联责任代码")
    private String relDutyCode;

    /**
     * 关联责任名称
     */
    @ApiModelProperty("关联责任名称")
    private String relDutyName;

    /**
     * 关联责任明细代码
     */
    @ApiModelProperty("关联责任明细代码")
    private String relDutyDetailCode;

    /**
     * 关联责任明细名称
     */
    @ApiModelProperty("关联责任明细名称")
    private String relDutyDetailName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
