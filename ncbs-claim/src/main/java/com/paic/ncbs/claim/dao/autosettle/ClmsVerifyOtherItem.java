package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款核责配置-其他核责项字典表
 */
@ApiModel("条款核责配置-其他核责项字典表")
@Getter
@Setter
public class ClmsVerifyOtherItem {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 风险类型
     */
    @ApiModelProperty("风险类型")
    private String verifyType;

    /**
     * 核责项名称
     */
    @ApiModelProperty("核责项名称")
    private String verifyName;

    /**
     * 核责等级
     */
    @ApiModelProperty("核责等级")
    private String verifyLevel;

    /**
     * 实现类
     */
    @ApiModelProperty("实现类")
    private String implClass;

    /**
     * 影响流程类型
     */
    @ApiModelProperty("影响流程类型")
    private String processFlag;

    /**
     * 说明
     */
    @ApiModelProperty("说明")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
