package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyHospital;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-医院范围表Mapper
 */
public interface ClmsVerifyHospitalMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyHospital selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyHospital record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyHospital record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyHospital record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyHospital record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyHospital> selectAll();

}
