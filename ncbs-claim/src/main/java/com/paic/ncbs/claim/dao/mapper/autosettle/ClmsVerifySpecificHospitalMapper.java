package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifySpecificHospital;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-特定医院表Mapper
 */
public interface ClmsVerifySpecificHospitalMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifySpecificHospital selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifySpecificHospital record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifySpecificHospital record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifySpecificHospital record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifySpecificHospital record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifySpecificHospital> selectAll();

}
