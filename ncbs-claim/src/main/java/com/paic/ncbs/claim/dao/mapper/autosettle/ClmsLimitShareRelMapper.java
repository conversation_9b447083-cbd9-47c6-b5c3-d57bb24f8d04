package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsLimitShareRel;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-费用共享约束被关联方表Mapper
 */
public interface ClmsLimitShareRelMapper {

    /**
     * 根据主键查询
     */
    ClmsLimitShareRel selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsLimitShareRel record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsLimitShareRel record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsLimitShareRel record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsLimitShareRel record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsLimitShareRel> selectAll();

}
