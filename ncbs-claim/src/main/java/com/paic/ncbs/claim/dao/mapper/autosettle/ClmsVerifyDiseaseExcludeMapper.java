package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyDiseaseExclude;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-疾病范围除外疾病表Mapper
 */
public interface ClmsVerifyDiseaseExcludeMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyDiseaseExclude selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyDiseaseExclude record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyDiseaseExclude record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyDiseaseExclude record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyDiseaseExclude record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyDiseaseExclude> selectAll();

}
