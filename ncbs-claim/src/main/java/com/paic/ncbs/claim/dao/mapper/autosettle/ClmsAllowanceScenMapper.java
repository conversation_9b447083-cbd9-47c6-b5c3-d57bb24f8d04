package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceScen;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-理算津贴类场景配置表Mapper
 */
public interface ClmsAllowanceScenMapper {

    /**
     * 根据主键查询
     */
    ClmsAllowanceScen selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsAllowanceScen record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsAllowanceScen record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsAllowanceScen record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsAllowanceScen record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsAllowanceScen> selectAll();

}
