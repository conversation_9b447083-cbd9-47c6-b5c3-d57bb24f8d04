package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmount;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-差异化额度表Mapper
 */
public interface ClmsDifferAmountMapper {

    /**
     * 根据主键查询
     */
    ClmsDifferAmount selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsDifferAmount record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsDifferAmount record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsDifferAmount record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsDifferAmount record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsDifferAmount> selectAll();

}
