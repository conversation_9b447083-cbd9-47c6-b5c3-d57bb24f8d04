package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDir;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 医疗目录表Mapper
 */
public interface ClmsMedicalDirMapper {

    /**
     * 根据主键查询
     */
    ClmsMedicalDir selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsMedicalDir record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsMedicalDir record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsMedicalDir record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsMedicalDir record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsMedicalDir> selectAll();

}
