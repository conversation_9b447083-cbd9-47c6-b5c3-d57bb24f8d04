package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Medicine;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款核责配置-三目录除外除外药品表Mapper
 */
public interface ClmsVerifyCatalog3MedicineMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyCatalog3Medicine selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyCatalog3Medicine record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyCatalog3Medicine record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyCatalog3Medicine record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyCatalog3Medicine record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyCatalog3Medicine> selectAll();

}
