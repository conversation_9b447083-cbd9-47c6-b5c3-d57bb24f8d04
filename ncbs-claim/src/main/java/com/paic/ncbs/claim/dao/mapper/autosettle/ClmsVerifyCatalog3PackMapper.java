package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Pack;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * Mapper
 */
public interface ClmsVerifyCatalog3PackMapper {

    /**
     * 根据主键查询
     */
    ClmsVerifyCatalog3Pack selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsVerifyCatalog3Pack record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsVerifyCatalog3Pack record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsVerifyCatalog3Pack record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsVerifyCatalog3Pack record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsVerifyCatalog3Pack> selectAll();

}
