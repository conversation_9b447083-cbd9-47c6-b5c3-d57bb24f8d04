package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatch;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款匹责配置表Mapper
 */
public interface ClmsClauseMatchMapper {

    /**
     * 根据主键查询
     */
    ClmsClauseMatch selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsClauseMatch record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsClauseMatch record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsClauseMatch record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsClauseMatch record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsClauseMatch> selectAll();

    /**
     * 根据配置ID查询匹责规则，按优先级排序
     */
    List<ClmsClauseMatch> getMatchRulesByConfigId(@Param("configId") Integer configId);

}
