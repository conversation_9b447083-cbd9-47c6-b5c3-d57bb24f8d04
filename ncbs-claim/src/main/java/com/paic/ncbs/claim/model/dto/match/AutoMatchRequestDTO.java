package com.paic.ncbs.claim.model.dto.match;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 自动匹责请求DTO
 */
@Data
public class AutoMatchRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 报案号 */
    private String reportNo;
    
    /** 赔付次数 */
    private Integer caseTimes;
    
    /** 产品代码 */
    private String productCode;
    
    /** 方案代码 */
    private String planCode;
    
    /** 发票信息列表 */
    private List<BillInfoForMatchDTO> billInfoList;
}

/**
 * 用于匹责的发票信息DTO
 */
@Data
class BillInfoForMatchDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 发票ID */
    private String idAhcsBillInfo;
    
    /** 发票号 */
    private String billNo;
    
    /** 发票类型 */
    private String billType;
    
    /** 主诊断ICD代码 */
    private String mainDiagnoseIcd;
    
    /** 主诊断名称 */
    private String mainDiagnoseName;
    
    /** 费用明细列表 */
    private List<BillDetailForMatchDTO> billDetailList;
}

/**
 * 用于匹责的费用明细DTO
 */
@Data
class BillDetailForMatchDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 费用项目 */
    private String costCode;
    
    /** 费用名称 */
    private String costName;
    
    /** 费用类型 */
    private String costType;
}
