package com.paic.ncbs.claim.service.match.impl;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig;
import com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatch;
import com.paic.ncbs.claim.dao.autosettle.ClmsInvoiceMatch;
import com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseConfigMapper;
import com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseMatchMapper;
import com.paic.ncbs.claim.dao.mapper.autosettle.ClmsInvoiceMatchMapper;
import com.paic.ncbs.claim.dao.mapper.autosettle.ClmsMedicalIcdDirMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.match.AutoMatchRequestDTO;
import com.paic.ncbs.claim.model.dto.match.AutoMatchResultDTO;
import com.paic.ncbs.claim.model.dto.match.InvoiceMatchDTO;
import com.paic.ncbs.claim.service.match.InvoiceMatchService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 发票匹责服务实现类
 */
@Service
public class InvoiceMatchServiceImpl implements InvoiceMatchService {
    
    @Autowired
    private ClmsInvoiceMatchMapper invoiceMatchMapper;
    
    @Autowired
    private ClmsClauseConfigMapper clauseConfigMapper;
    
    @Autowired
    private ClmsClauseMatchMapper clauseMatchMapper;
    
    @Autowired
    private ClmsMedicalIcdDirMapper medicalIcdDirMapper;
    
    @Override
    @Transactional
    public void saveManualMatch(List<InvoiceMatchDTO> matchList, String userUM) throws GlobalBusinessException {
        if (CollectionUtils.isEmpty(matchList)) {
            throw new GlobalBusinessException("匹责结果列表不能为空");
        }

        String reportNo = matchList.get(0).getReportNo();
        Integer caseTimes = matchList.get(0).getCaseTimes();

        // 先删除原有的匹责结果
        deleteMatchResults(reportNo, caseTimes, userUM);

        // 保存新的匹责结果
        Date now = new Date();
        for (InvoiceMatchDTO matchDTO : matchList) {
            ClmsInvoiceMatch match = convertToEntity(matchDTO);
            match.setMatchType("MANUAL");
            match.setMatchStatus("SUCCESS");
            match.setCreatedBy(userUM);
            match.setSysCtime(now);
            match.setUpdatedBy(userUM);
            match.setSysUtime(now);

            invoiceMatchMapper.insert(match);
        }

        LogUtil.audit("保存人工匹责结果成功，报案号：{}，赔付次数：{}，匹责数量：{}", reportNo, caseTimes, matchList.size());
    }
    
    @Override
    public AutoMatchResultDTO autoMatch(AutoMatchRequestDTO request) throws GlobalBusinessException {
        LogUtil.audit("开始自动匹责，报案号：{}，赔付次数：{}", request.getReportNo(), request.getCaseTimes());
        
        AutoMatchResultDTO result = new AutoMatchResultDTO();
        result.setReportNo(request.getReportNo());
        result.setCaseTimes(request.getCaseTimes());
        result.setMatchResults(new ArrayList<>());
        
        try {
            // 1. 检查是否需要自动匹责
            if (!needAutoMatch(request.getReportNo(), request.getCaseTimes(), request.getProductCode())) {
                result.setSuccess(false);
                result.setFailReason("不需要自动匹责");
                return result;
            }
            
            // 2. 查找有效的理赔配置
            Integer configId = clauseConfigMapper.getEffectiveConfigId(request.getProductCode(), request.getPlanCode());
            if (configId == null) {
                result.setSuccess(false);
                result.setFailReason("未找到有效的理赔配置项");
                LogUtil.audit("未找到有效的理赔配置项，产品代码：{}，方案代码：{}", request.getProductCode(), request.getPlanCode());
                return result;
            }
            
            // 3. 执行自动匹责逻辑
            List<InvoiceMatchDTO> matchResults = performAutoMatch(request, configId);
            
            if (CollectionUtils.isNotEmpty(matchResults)) {
                // 4. 保存匹责结果
                saveAutoMatchResults(matchResults, "system");
                result.setMatchResults(matchResults);
                result.setSuccess(true);
                LogUtil.audit("自动匹责成功，报案号：{}，赔付次数：{}，匹责数量：{}", 
                    request.getReportNo(), request.getCaseTimes(), matchResults.size());
            } else {
                result.setSuccess(false);
                result.setFailReason("未找到匹配的责任明细");
                LogUtil.audit("自动匹责失败，未找到匹配的责任明细");
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setFailReason("自动匹责异常：" + e.getMessage());
            LogUtil.audit("自动匹责异常：{}", e.getMessage());
            throw new GlobalBusinessException("自动匹责失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行自动匹责逻辑
     */
    private List<InvoiceMatchDTO> performAutoMatch(AutoMatchRequestDTO request, Integer configId) {
        List<InvoiceMatchDTO> results = new ArrayList<>();
        
        // 查询匹责规则，按优先级排序
        List<Object> matchRules = clauseMatchMapper.getMatchRulesByConfigId(configId);
        
        if (CollectionUtils.isEmpty(matchRules)) {
            LogUtil.audit("未找到匹责规则，configId：{}", configId);
            return results;
        }
        
        // 对每张发票进行匹责
        for (Object billInfo : request.getBillInfoList()) {
            InvoiceMatchDTO matchResult = matchSingleBill(billInfo, matchRules, request);
            if (matchResult != null) {
                results.add(matchResult);
            }
        }
        
        return results;
    }
    
    /**
     * 对单张发票进行匹责
     */
    private InvoiceMatchDTO matchSingleBill(Object billInfo, List<Object> matchRules, AutoMatchRequestDTO request) {
        // 这里需要根据具体的匹责规则进行实现
        // 1. 检查发票类型匹配
        // 2. 检查疾病分类匹配
        // 3. 检查票据明细匹配
        
        // 示例实现，实际需要根据业务规则完善
        InvoiceMatchDTO result = new InvoiceMatchDTO();
        result.setIdInvoiceMatch(UuidUtil.getUUID());
        result.setReportNo(request.getReportNo());
        result.setCaseTimes(request.getCaseTimes());
        result.setProductCode(request.getProductCode());
        result.setPlanCode(request.getPlanCode());
        result.setMatchType("AUTO");
        result.setMatchStatus("SUCCESS");
        result.setMatchReason("自动匹责成功");
        
        Date now = new Date();
        result.setCreatedDate(now);
        result.setUpdatedDate(now);
        result.setArchiveTime(now);
        
        return result;
    }
    
    /**
     * 保存自动匹责结果
     */
    @Transactional
    private void saveAutoMatchResults(List<InvoiceMatchDTO> matchResults, String userUM) {
        if (CollectionUtils.isEmpty(matchResults)) {
            return;
        }
        
        String reportNo = matchResults.get(0).getReportNo();
        Integer caseTimes = matchResults.get(0).getCaseTimes();
        
        // 先删除原有的自动匹责结果
        invoiceMatchMapper.deleteByReportNoAndCaseTimesAndType(reportNo, caseTimes, "AUTO");
        
        // 保存新的匹责结果
        for (InvoiceMatchDTO match : matchResults) {
            match.setCreatedBy(userUM);
            match.setUpdatedBy(userUM);
            invoiceMatchMapper.insert(match);
        }
    }
    
    @Override
    public List<InvoiceMatchDTO> getMatchResults(String reportNo, Integer caseTimes) {
        List<ClmsInvoiceMatch> entities = invoiceMatchMapper.selectByReportNoAndCaseTimes(reportNo, caseTimes);
        List<InvoiceMatchDTO> results = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entities)) {
            for (ClmsInvoiceMatch entity : entities) {
                results.add(convertToDTO(entity));
            }
        }
        return results;
    }

    @Override
    public List<InvoiceMatchDTO> getMatchResultsByBillId(String idAhcsBillInfo) {
        List<ClmsInvoiceMatch> entities = invoiceMatchMapper.selectByBillId(idAhcsBillInfo);
        List<InvoiceMatchDTO> results = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entities)) {
            for (ClmsInvoiceMatch entity : entities) {
                results.add(convertToDTO(entity));
            }
        }
        return results;
    }
    
    @Override
    @Transactional
    public void deleteMatchResults(String reportNo, Integer caseTimes, String userUM) {
        invoiceMatchMapper.deleteByReportNoAndCaseTimes(reportNo, caseTimes);
        LogUtil.audit("删除匹责结果成功，报案号：{}，赔付次数：{}", reportNo, caseTimes);
    }
    
    @Override
    public boolean needAutoMatch(String reportNo, Integer caseTimes, String productCode) {
        // 1. 检查产品线是否为意外险或健康险
        if (!isHealthOrAccidentProduct(productCode)) {
            LogUtil.audit("产品线非意外险/健康险，不需要自动匹责，产品代码：{}", productCode);
            return false;
        }
        
        // 2. 检查是否已有人工匹责结果
        List<InvoiceMatchDTO> existingMatches = getMatchResults(reportNo, caseTimes);
        if (CollectionUtils.isNotEmpty(existingMatches)) {
            boolean hasManualMatch = existingMatches.stream()
                .anyMatch(match -> "MANUAL".equals(match.getMatchType()));
            if (hasManualMatch) {
                LogUtil.audit("已存在人工匹责结果，不需要自动匹责，报案号：{}，赔付次数：{}", reportNo, caseTimes);
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public void validateManualMatch(String reportNo, Integer caseTimes, String productCode) throws GlobalBusinessException {
        // 检查产品大类是否为健康险/意外险且为个险
        if (!isHealthOrAccidentProduct(productCode)) {
            return; // 非健康险/意外险不需要验证
        }
        
        // 检查是否关联了至少一个责任明细
        List<InvoiceMatchDTO> matchResults = getMatchResults(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(matchResults)) {
            throw new GlobalBusinessException("当产品大类=健康险/意外险，且个险时，录入定损信息保存时，需要关联至少一个责任明细");
        }
        
        LogUtil.audit("人工匹责验证通过，报案号：{}，赔付次数：{}", reportNo, caseTimes);
    }
    
    /**
     * 检查是否为健康险或意外险产品
     */
    private boolean isHealthOrAccidentProduct(String productCode) {
        // 这里需要根据实际的产品分类逻辑进行判断
        // 示例实现
        return StringUtils.isNotBlank(productCode) &&
               (productCode.startsWith("H") || productCode.startsWith("A"));
    }

    /**
     * 将DTO转换为实体类
     */
    private ClmsInvoiceMatch convertToEntity(InvoiceMatchDTO dto) {
        ClmsInvoiceMatch entity = new ClmsInvoiceMatch();
        entity.setReportNo(dto.getReportNo());
        entity.setCaseTimes(dto.getCaseTimes());
        entity.setIdAhcsInvoiceInfo(dto.getIdAhcsBillInfo());
        entity.setMatchType(dto.getMatchType());
        // 将匹责状态和原因合并到匹责结论字段
        String matchResult = dto.getMatchStatus() + ":" +
            (StringUtils.isNotBlank(dto.getMatchReason()) ? dto.getMatchReason() : "");
        entity.setMatchResult(matchResult);
        entity.setMatchSign((byte) ("SUCCESS".equals(dto.getMatchStatus()) ? 1 : 0));
        return entity;
    }

    /**
     * 将实体类转换为DTO
     */
    private InvoiceMatchDTO convertToDTO(ClmsInvoiceMatch entity) {
        InvoiceMatchDTO dto = new InvoiceMatchDTO();
        dto.setReportNo(entity.getReportNo());
        dto.setCaseTimes(entity.getCaseTimes());
        dto.setIdAhcsBillInfo(entity.getIdAhcsInvoiceInfo());
        dto.setMatchType(entity.getMatchType());

        // 从匹责结论字段解析状态和原因
        String matchResult = entity.getMatchResult();
        if (StringUtils.isNotBlank(matchResult) && matchResult.contains(":")) {
            String[] parts = matchResult.split(":", 2);
            dto.setMatchStatus(parts[0]);
            dto.setMatchReason(parts.length > 1 ? parts[1] : "");
        } else {
            dto.setMatchStatus(entity.getMatchSign() != null && entity.getMatchSign() == 1 ? "SUCCESS" : "FAILED");
            dto.setMatchReason(StringUtils.isNotBlank(entity.getFailDesc()) ? entity.getFailDesc() : "");
        }

        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreatedDate(entity.getSysCtime());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdatedDate(entity.getSysUtime());
        return dto;
    }
}
