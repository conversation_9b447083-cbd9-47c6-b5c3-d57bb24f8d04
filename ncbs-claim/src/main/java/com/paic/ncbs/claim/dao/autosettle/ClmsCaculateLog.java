package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 自动理算日志主表
 */
@ApiModel("自动理算日志主表")
@Getter
@Setter
public class ClmsCaculateLog {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 案件号
     */
    @ApiModelProperty("案件号")
    private String reportNo;

    /**
     * 赔付次数
     */
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    /**
     * 匹配保单
     */
    @ApiModelProperty("匹配保单")
    private String policyNo;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 理算次数
     */
    @ApiModelProperty("理算次数")
    private Integer calcTimes;

    /**
     * 计算赔款金额（扣除非则部分）
     */
    @ApiModelProperty("计算赔款金额（扣除非则部分）")
    private BigDecimal sumPay;

    /**
     * 计算免责金额
     */
    @ApiModelProperty("计算免责金额")
    private BigDecimal disclaimSumPay;

    /**
     * 当前有效标志(最后一次记录有效，历史的记录无效)
     */
    @ApiModelProperty("当前有效标志(最后一次记录有效，历史的记录无效)")
    private Boolean validFlag;

    /**
     * 理算依据
     */
    @ApiModelProperty("理算依据")
    private String calcAccording;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
