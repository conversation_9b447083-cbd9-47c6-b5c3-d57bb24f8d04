package com.paic.ncbs.claim.dao.mapper.settle;


import com.paic.ncbs.claim.model.dto.report.ReportPolicyInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.vo.openapi.CompositePolicyVO;
import com.paic.ncbs.claim.model.vo.openapi.PolicyVO;
import com.paic.ncbs.claim.model.vo.settle.LifeInsuranceVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyAndCustomerInfoVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyInfoVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@MapperScan
public interface PolicyInfoMapper {

	public List<PolicyInfoVO> getPolicyStatus(@Param("reportNo") String reportNo);

	public List<PolicyInfoVO> getPolicyInfo(@Param("reportNo") String reportNo);

	public List<PolicyInfoVO> getPolicyintoList(String reportNo);

	public List<PolicyAndCustomerInfoVO> getPolicyAndCustomerInfoList(@Param("reportNo") String reportNo);

	public List<PolicyInfoDTO> getPolicyInfoListByReportNo(@Param("reportNo") String reportNo);

	public List<PolicyInfoDTO> getPolicyInfoListByDutyCode(@Param("reportNo") String reportNo,
														   @Param("dutyCode") String dutyCode);
	public List<PolicyInfoDTO> getEHisPolicies(@Param("reportNo") String reportNo, @Param("prodCodeList") Set<String> prodCodeList);

	public String getInsuredPersonCerNo(@Param("reportNo") String reportNo);

	public List<LifeInsuranceVO> getLifeInsuranceInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

	public PolicyInfoDTO getPolicyCerNoById(@Param("idPolicyInfo") String idPolicyInfo);

	public PolicyInfoDTO getIdPolicyInfo(@Param("idClmCaseBase") String idClmCaseBase, @Param("reportNo") String reportNo);

	public PolicyInfoDTO getIdPolicyInfoByCaseNo(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo);

	public PolicyInfoDTO getPolicyValidityPeriod(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo);

	public List<String> getReportPlanByCaseNo(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo, @Param("totalInsuredAmount") BigDecimal totalInsuredAmount);

	public List<String> getEndCasePlanByCaseNo(@Param("caseNo") String caseNo, @Param("caseTimes") Integer caseTimes);

	/**
	 * 根据报案号获取产品代码
	 */
	public String getProductCodeByReportNo(@Param("reportNo") String reportNo);

	public List<PolicyInfoDTO> getDeptListByUploadCase(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("level2Dept") String level2Dept);

	public String getPolicyDeptByCaseNo(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo);

	public List<String> getProductCodeByCaseNo(@Param("reportNo") String reportNo, @Param("caseNoList") List<String> caseNoList);

	public List<String> getCaseNoByReportProduct(@Param("reportNo") String reportNo, @Param("productCodeList") Set<String> productCodeList);

	public List<String> getPolicyNoByCaseNo(@Param("reportNo") String reportNo, @Param("caseNoList") List<String> caseNoList);

	public List<String> getPolicyNoByProductCode(@Param("reportNo") String reportNo, @Param("productCodeList") Set<String> productCodeList);

    public String getRandomPolicyByReport(@Param("reportNo") String reportNo);

	public ReportPolicyInfoDTO getReportPolicyInfoByCaseNo(@Param("caseNo") String caseNo);

	public List<String> getHisCaseNoList(@Param("policyNo") String policyNo, @Param("policyCerNo") String policyCerNo,
										 @Param("clientNo") String clientNo);

	String getPolicyDeptName(@Param("reportNo") String reportNo, @Param("caseNo") String caseNo);

	List<String> getPolicyNo(@Param("reportNo") String reportNo);

	String getPolicyDeptByReportNo(@Param("reportNo") String reportNo);

	List<String> getAccidentDateStr(@Param("policyNo") String policyNo,@Param("certificateNo") String certificateNo,@Param("name") String name);

	List<PolicyVO> getOpenPolicyInfoList(@Param("reportNo") String reportNo);

	List<CompositePolicyVO> getCompositePolicy(@Param("reportNo") String reportNo);

	List<CompositePolicyVO> getCompositePolicyByPolicyNo(@Param("reportNo") String reportNo,@Param("policyNo") String policyNo);

	String getPackageType(@Param("reportNo") String reportNo,@Param("policyNo") String policyNo);
}
