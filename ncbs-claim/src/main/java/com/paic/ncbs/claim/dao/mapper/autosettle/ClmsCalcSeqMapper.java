package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsCalcSeq;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-理算顺序Mapper
 */
public interface ClmsCalcSeqMapper {

    /**
     * 根据主键查询
     */
    ClmsCalcSeq selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsCalcSeq record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsCalcSeq record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsCalcSeq record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsCalcSeq record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsCalcSeq> selectAll();

}
