package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsFeeConfig;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 条款理算配置-费用约束表Mapper
 */
public interface ClmsFeeConfigMapper {

    /**
     * 根据主键查询
     */
    ClmsFeeConfig selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsFeeConfig record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsFeeConfig record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsFeeConfig record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsFeeConfig record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsFeeConfig> selectAll();

}
