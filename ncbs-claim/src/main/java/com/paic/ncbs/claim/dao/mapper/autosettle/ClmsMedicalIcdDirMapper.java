package com.paic.ncbs.claim.dao.mapper.autosettle;

import com.paic.ncbs.claim.dao.autosettle.ClmsMedicalIcdDir;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 医疗ICD目录关系表Mapper
 */
public interface ClmsMedicalIcdDirMapper {

    /**
     * 根据主键查询
     */
    ClmsMedicalIcdDir selectByPrimaryKey(@Param("id") Integer id);

    /**
     * 插入记录
     */
    int insert(ClmsMedicalIcdDir record);

    /**
     * 选择性插入记录
     */
    int insertSelective(ClmsMedicalIcdDir record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(ClmsMedicalIcdDir record);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(ClmsMedicalIcdDir record);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(@Param("id") Integer id);

    /**
     * 查询所有记录
     */
    List<ClmsMedicalIcdDir> selectAll();

}
